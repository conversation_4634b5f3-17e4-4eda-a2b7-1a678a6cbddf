'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { ChevronDown } from 'lucide-react';
import { useState } from 'react';

export default function BillingAddressForm() {
  const [address, setAddress] = useState({
    line1: 'C60 An Thinh Residence',
    line2: 'Ba Diem Ward',
    city: 'Hoc Mon',
    state: 'Ho Chi Minh',
    postalCode: '700000',
    country: 'Viet Nam',
  });

  const handleChange = (field: string, value: string) => {
    setAddress((prev) => ({ ...prev, [field]: value }));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Địa chỉ liên hệ</CardTitle>
        <CardDescription>
          Nếu bạn muốn thêm địa chỉ g<PERSON>i hóa đơn, vui lòng nhập vào đây.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Input
          value={address.line1}
          onChange={(e) => handleChange('line1', e.target.value)}
          placeholder="Address line 1"
        />
        <Input
          value={address.line2}
          onChange={(e) => handleChange('line2', e.target.value)}
          placeholder="Address line 2"
        />
        <Input
          value={address.city}
          onChange={(e) => handleChange('city', e.target.value)}
          placeholder="City"
        />
        <div className="grid grid-cols-2 gap-4">
          <div className="relative">
            <Input
              value={address.state}
              onChange={(e) => handleChange('state', e.target.value)}
              placeholder="State/Province"
            />
            <ChevronDown className="pointer-events-none absolute top-1/2 right-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
          </div>
          <Input
            value={address.postalCode}
            onChange={(e) => handleChange('postalCode', e.target.value)}
            placeholder="Postal code"
          />
        </div>
        <div className="relative">
          <Input
            value={address.country}
            onChange={(e) => handleChange('country', e.target.value)}
            placeholder="Country"
          />
          <ChevronDown className="pointer-events-none absolute top-1/2 right-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
        </div>
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button variant="default" size="sm">
          Lưu lại
        </Button>
      </CardFooter>
    </Card>
  );
}
