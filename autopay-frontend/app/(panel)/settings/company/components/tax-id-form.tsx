'use client';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { useState } from 'react';

export default function TaxIdForm() {
  const [taxType, setTaxType] = useState('eu-vat');
  const [taxId, setTaxId] = useState('');

  return (
    <Card>
      <CardHeader>
        <CardTitle>Mã số thuế</CardTitle>
        <CardDescription>
          Nếu bạn muốn hóa đơn của mình hiển thị một mã số thuế cụ thể, hãy nhập
          vào đây.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Input
          placeholder="*********"
          value={taxId}
          onChange={(e) => setTaxId(e.target.value)}
        />
      </CardContent>
      <CardFooter className="flex items-center justify-between">
        <p className="text-muted-foreground text-xs">
          Nhập mã số thuế của bạn để hiển thị trên hóa đơn.
        </p>
        <Button variant="default" size="sm">
          Lưu lại
        </Button>
      </CardFooter>
    </Card>
  );
}
