'use client';
import {
  DescriptionDetail,
  DescriptionGroup,
  DescriptionList,
  DescriptionTerm,
} from '@/components/custom-ui/description-list';
import { Badge } from '@/components/ui/badge';
import { FaCircleCheck } from 'react-icons/fa6';
import { FiLoader } from 'react-icons/fi';

export default function Component() {
  // Random boolean value
  const rand = Math.random() < 0.5;

  return (
    <DescriptionList>
      <DescriptionGroup>
        <DescriptionTerm className="">Trạng thái</DescriptionTerm>
        <DescriptionDetail>
          <Badge variant="outline" className="text-muted-foreground px-1.5">
            {rand ? (
              <>
                <FaCircleCheck className="fill-green-500 dark:fill-green-400" />{' '}
                Đang hoạt động
              </>
            ) : (
              <>
                <FiLoader /> Không hoạt động
              </>
            )}
          </Badge>
        </DescriptionDetail>
      </DescriptionGroup>

      <DescriptionGroup>
        <DescriptionTerm className=""><PERSON><PERSON><PERSON> hết hạn</DescriptionTerm>
        <DescriptionDetail>09/04/2025 12:34 (còn 30 ngày)</DescriptionDetail>
      </DescriptionGroup>

      <DescriptionGroup>
        <DescriptionTerm className="">Số tài khoản chính</DescriptionTerm>
        <DescriptionDetail>10</DescriptionDetail>
      </DescriptionGroup>

      <DescriptionGroup>
        <DescriptionTerm className="">
          Số tài khoản VA/ luồng tiền
        </DescriptionTerm>
        <DescriptionDetail>4</DescriptionDetail>
      </DescriptionGroup>

      <DescriptionGroup>
        <DescriptionTerm className="">Số thành viên</DescriptionTerm>
        <DescriptionDetail>4</DescriptionDetail>
      </DescriptionGroup>
      <DescriptionGroup>
        <DescriptionTerm className="">Ngày khởi tạo</DescriptionTerm>
        <DescriptionDetail>09/03/2025 12:34</DescriptionDetail>
      </DescriptionGroup>
    </DescriptionList>
  );
}
