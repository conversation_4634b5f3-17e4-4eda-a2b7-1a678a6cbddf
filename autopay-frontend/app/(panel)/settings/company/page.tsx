'use client'

import PageHeading from '@/app/(panel)/common/components/page-heading'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'

import { Button } from '@/components/ui/button'

import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import BillingForm from './components/billing-address-form'
import Summary from './components/summary'
import TaxForm from './components/tax-id-form'

export default function Component() {
  return (
    <div className="space-y-6">
      <PageHeading
        title="<PERSON>ồ sơ công ty"
        description="C<PERSON>u hình thông tin cơ bản về tài khoản công ty của bạn."
      />
      <div className="mb-6 flex flex-col justify-between gap-4 md:flex-row">
        <div className="space-y-6 md:w-2/3">
          <Card>
            <CardHeader>
              <CardTitle>Tên công ty</CardTitle>
              <CardDescription className="mt-2">
                Mặc định, tên nhóm sẽ được hiển thị trên hóa đơn của bạn. Nếu bạn muốn hiển thị một tên tùy chỉnh thay
                thế, vui lòng nhập vào đây.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid w-full max-w-sm items-center gap-1.5">
                <Label htmlFor="picture">Tên đầy đủ</Label>
                <Input
                  id="picture"
                  maxLength={64}
                  placeholder="CTY TNHH ABC"
                />
                <p className="text-muted-foreground text-xs">Nhập tối đa 64 ký tự.</p>
              </div>

              <div className="grid w-full max-w-sm items-center gap-1.5">
                <Label htmlFor="picture">Tên viết tắt</Label>
                <Input
                  id="picture"
                  maxLength={64}
                  placeholder="Viettel, Vingroup, Vnpt"
                />
                <p className="text-muted-foreground text-xs">Nhập tối đa 64 ký tự.</p>
              </div>
            </CardContent>
            <CardFooter className="flex items-center justify-end">
              <Button
                variant="default"
                size="sm">
                Lưu lại
              </Button>
            </CardFooter>
          </Card>
          <BillingForm />
          <TaxForm />
        </div>
        <Card className="h-fit md:w-1/3">
          <CardHeader>
            <CardTitle>AUTOPAY</CardTitle>
            <CardDescription className="mt-2">CTY TNHH AUTOPAY</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Summary />
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
